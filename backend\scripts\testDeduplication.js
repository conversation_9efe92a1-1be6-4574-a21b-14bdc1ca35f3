/**
 * 测试去重功能的脚本
 * 用于验证去重逻辑是否正常工作
 */

const { db } = require('../config/database')

async function testDeduplication() {
  try {
    console.log('🧪 开始测试去重功能...\n')

    // 1. 获取 plants_list 中的所有植物名称
    console.log('📋 获取 plants_list 中的植物名称...')
    const plantsListResult = await db.collection('plants_list').get()
    const existingPlantNames = new Set()
    
    if (plantsListResult.data && plantsListResult.data.length > 0) {
      plantsListResult.data.forEach(item => {
        if (item.name && item.name.trim()) {
          existingPlantNames.add(item.name.trim())
        }
      })
    }
    
    console.log(`✅ plants_list 中共有 ${existingPlantNames.size} 个植物名称`)
    console.log('前10个植物名称:', Array.from(existingPlantNames).slice(0, 10))
    console.log('')

    // 2. 测试 notice 集合的去重
    console.log('📋 测试 notice 集合的去重...')
    const noticeResult = await db.collection('notice').get()
    const noticeData = noticeResult.data || []
    
    console.log(`notice 集合原始数据量: ${noticeData.length}`)
    
    const noticeFiltered = noticeData.filter(item => {
      if (!item.plantName || !item.plantName.trim()) {
        return true // 保留空的植物名称（可以编辑）
      }
      return !existingPlantNames.has(item.plantName.trim())
    })
    
    const noticeDuplicateCount = noticeData.length - noticeFiltered.length
    console.log(`notice 集合去重后数据量: ${noticeFiltered.length}`)
    console.log(`notice 集合重复数据量: ${noticeDuplicateCount}`)
    console.log('')

    // 3. 测试 searchWord 集合的去重
    console.log('📋 测试 searchWord 集合的去重...')
    const searchWordResult = await db.collection('searchWord').get()
    const searchWordData = searchWordResult.data || []
    
    console.log(`searchWord 集合原始数据量: ${searchWordData.length}`)
    
    const searchWordFiltered = searchWordData.filter(item => {
      if (!item.plantName || !item.plantName.trim()) {
        return true // 保留空的植物名称（可以编辑）
      }
      return !existingPlantNames.has(item.plantName.trim())
    })
    
    const searchWordDuplicateCount = searchWordData.length - searchWordFiltered.length
    console.log(`searchWord 集合去重后数据量: ${searchWordFiltered.length}`)
    console.log(`searchWord 集合重复数据量: ${searchWordDuplicateCount}`)
    console.log('')

    // 4. 显示一些重复的植物名称示例
    console.log('🔍 重复植物名称示例:')
    let duplicateExamples = []
    
    noticeData.forEach(item => {
      if (item.plantName && item.plantName.trim() && existingPlantNames.has(item.plantName.trim())) {
        duplicateExamples.push(`notice: ${item.plantName.trim()}`)
      }
    })
    
    searchWordData.forEach(item => {
      if (item.plantName && item.plantName.trim() && existingPlantNames.has(item.plantName.trim())) {
        duplicateExamples.push(`searchWord: ${item.plantName.trim()}`)
      }
    })
    
    console.log('前10个重复示例:', duplicateExamples.slice(0, 10))
    console.log('')

    // 5. 总结
    console.log('📊 去重测试总结:')
    console.log(`plants_list 中的植物名称数量: ${existingPlantNames.size}`)
    console.log(`notice 集合: ${noticeData.length} → ${noticeFiltered.length} (过滤 ${noticeDuplicateCount} 条)`)
    console.log(`searchWord 集合: ${searchWordData.length} → ${searchWordFiltered.length} (过滤 ${searchWordDuplicateCount} 条)`)
    console.log(`总重复数据量: ${noticeDuplicateCount + searchWordDuplicateCount}`)
    
    console.log('\n✅ 去重功能测试完成!')

  } catch (error) {
    console.error('❌ 测试失败:', error)
    console.error('错误详情:', error.message)
  }
}

// 运行测试
if (require.main === module) {
  testDeduplication().then(() => {
    console.log('\n🎉 测试脚本执行完成')
    process.exit(0)
  }).catch(error => {
    console.error('\n💥 测试脚本执行失败:', error)
    process.exit(1)
  })
}

module.exports = { testDeduplication }
