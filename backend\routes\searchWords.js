const express = require('express')
const router = express.Router()
const searchWordsController = require('../controllers/searchWordsController')

// 获取数据源数据 (notice 或 searchWord 集合)
router.get('/source/:source', searchWordsController.getSourceData)

// 更新植物名称
router.put('/update/:source/:id', searchWordsController.updatePlantName)

// 检索列表管理
router.post('/plants-list', searchWordsController.addToPlantsList)
router.get('/plants-list', searchWordsController.getPlantsList)
router.delete('/plants-list/:id', searchWordsController.deletePlantItem)

// 测试去重功能
router.get('/test-deduplication/:source', searchWordsController.testDeduplication)

module.exports = router