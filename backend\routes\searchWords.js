const express = require('express')
const router = express.Router()
const searchWordsController = require('../controllers/searchWordsController')

// 获取数据源数据 (notice 或 searchWord 集合)
router.get('/source/:source', searchWordsController.getSourceData)

// 获取去重后的数据源数据 (notice 或 searchWord 集合，排除已存在于 plants_list 中的数据)
router.get('/source/:source/deduped', searchWordsController.getSourceDataDeduped)

// 更新植物名称
router.put('/update/:source/:id', searchWordsController.updatePlantName)

// 检索列表管理
router.post('/plants-list', searchWordsController.addToPlantsList)
router.get('/plants-list', searchWordsController.getPlantsList)
router.delete('/plants-list/:id', searchWordsController.deletePlantItem)

module.exports = router