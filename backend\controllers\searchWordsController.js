const { db } = require('../config/database')
const Response = require('../utils/response')

// 获取数据源数据 (notice 或 searchWord 集合) - 带智能去重
const getSourceData = async (req, res) => {
  try {
    const { source } = req.params
    const { page = 1, limit = 20, enableDeduplication = 'true' } = req.query

    console.log(`获取数据源请求: source=${source}, page=${page}, limit=${limit}, enableDeduplication=${enableDeduplication}`)

    // 验证数据源
    if (!['notice', 'searchWord'].includes(source)) {
      console.log('无效的数据源:', source)
      return res.status(400).json(Response.error('无效的数据源', 400))
    }

    const offset = (parseInt(page) - 1) * parseInt(limit)
    console.log(`查询参数: offset=${offset}, limit=${limit}`)

    // 先测试数据库连接
    console.log('开始查询数据库...')

    let responseData

    if (enableDeduplication === 'true') {
      // 启用智能去重模式
      console.log('启用智能去重模式')
      responseData = await getDeduplicatedSourceData(source, parseInt(page), parseInt(limit))
    } else {
      // 传统模式，不去重
      console.log('传统模式，不去重')
      const [dataResult, countResult] = await Promise.all([
        db.collection(source)
          .skip(offset)
          .limit(parseInt(limit))
          .get(),
        db.collection(source).count()
      ])

      responseData = {
        data: dataResult.data || [],
        total: countResult.total || 0,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    }

    res.json(Response.success(responseData, `获取${source}数据成功`))
  } catch (err) {
    console.error('获取数据源数据失败:', err)
    console.error('错误详情:', err.message)
    res.status(500).json(Response.error(`获取数据失败: ${err.message}`))
  }
}

// 智能去重获取数据源数据
const getDeduplicatedSourceData = async (source, page, limit) => {
  try {
    console.log('开始智能去重处理...')

    // 1. 获取plants_list中所有的name字段作为去重基准
    console.log('获取plants_list中所有name字段...')
    const plantsListResult = await db.collection('plants_list').get()
    const existingNames = new Set()

    if (plantsListResult.data) {
      plantsListResult.data.forEach(item => {
        if (item.name && typeof item.name === 'string') {
          existingNames.add(item.name.trim().toLowerCase())
        }
      })
    }

    console.log(`plants_list中已存在${existingNames.size}个检索词`)

    // 2. 获取源集合的所有数据
    console.log(`获取${source}集合的所有数据...`)
    const sourceResult = await db.collection(source).get()
    const originalData = sourceResult.data || []
    const originalTotal = originalData.length

    console.log(`${source}集合原始数据量: ${originalTotal}`)

    // 3. 进行去重过滤
    const deduplicatedData = originalData.filter(item => {
      // 保留plantName为空或null的记录（允许管理员编辑后添加）
      if (!item.plantName || typeof item.plantName !== 'string') {
        return true
      }

      const trimmedPlantName = item.plantName.trim()
      if (!trimmedPlantName) {
        return true
      }

      // 检查是否与plants_list中的name重复
      return !existingNames.has(trimmedPlantName.toLowerCase())
    })

    const filteredTotal = deduplicatedData.length
    const duplicateCount = originalTotal - filteredTotal

    console.log(`去重结果: 原始${originalTotal}条, 过滤${duplicateCount}条重复, 剩余${filteredTotal}条`)

    // 4. 分页处理
    const offset = (page - 1) * limit
    const paginatedData = deduplicatedData.slice(offset, offset + limit)

    console.log(`分页结果: 第${page}页, 每页${limit}条, 返回${paginatedData.length}条`)

    return {
      data: paginatedData,
      total: filteredTotal,
      page: page,
      limit: limit,
      deduplicationStats: {
        originalTotal: originalTotal,
        duplicateCount: duplicateCount,
        availableTotal: filteredTotal,
        existingInPlantsListCount: existingNames.size
      }
    }
  } catch (error) {
    console.error('智能去重处理失败:', error)
    throw error
  }
}

// 更新植物名称
const updatePlantName = async (req, res) => {
  try {
    const { source, id } = req.params
    const { plantName } = req.body

    console.log(`更新植物名称请求: source=${source}, id=${id}, plantName=${plantName}`)

    // 验证数据源
    if (!['notice', 'searchWord'].includes(source)) {
      return res.status(400).json(Response.error('无效的数据源', 400))
    }

    // 验证植物名称
    if (!plantName || !plantName.trim()) {
      return res.status(400).json(Response.error('植物名称不能为空', 400))
    }

    // 先检查文档是否存在
    const docExists = await db.collection(source).doc(id).get()
    console.log('文档存在检查:', {
      exists: !!docExists.data,
      docId: id,
      currentData: docExists.data
    })

    if (!docExists.data) {
      console.log('文档不存在，返回404')
      return res.status(404).json(Response.error('未找到要更新的记录', 404))
    }

    // 更新数据
    const result = await db.collection(source)
      .doc(id)
      .update({
        plantName: plantName.trim()
      })

    console.log('更新结果详情:', JSON.stringify(result, null, 2))

    // 腾讯云开发数据库更新成功时，通常不会抛出异常
    // 如果执行到这里，说明更新成功
    console.log('更新操作完成')
    res.json(Response.success(null, '更新植物名称成功'))

  } catch (err) {
    console.error('更新植物名称失败:', err)
    console.error('错误详情:', err.message)
    console.error('错误堆栈:', err.stack)

    // 检查是否是文档不存在的错误
    if (err.message && (err.message.includes('not found') || err.message.includes('document does not exist'))) {
      res.status(404).json(Response.error('未找到要更新的记录', 404))
    } else {
      res.status(500).json(Response.error('更新失败: ' + err.message))
    }
  }
}

// 添加到检索列表 (plants_list)
const addToPlantsList = async (req, res) => {
  try {
    const { name } = req.body
    
    // 验证名称
    if (!name || !name.trim()) {
      return res.status(400).json(Response.error('检索名称不能为空', 400))
    }
    
    const trimmedName = name.trim()
    
    // 检查是否已存在
    const existResult = await db.collection('plants_list')
      .where({
        name: trimmedName
      })
      .get()
    
    if (existResult.data && existResult.data.length > 0) {
      return res.status(400).json(Response.error('该检索词已存在', 400))
    }
    
    // 添加新记录
    const result = await db.collection('plants_list').add({
      name: trimmedName,
      hit: 0 // 默认命中次数为0
    })
    
    if (result.id) {
      res.json(Response.success({ id: result.id }, '添加到检索列表成功'))
    } else {
      res.status(500).json(Response.error('添加失败'))
    }
  } catch (err) {
    console.error('添加到检索列表失败:', err)
    res.status(500).json(Response.error('添加失败'))
  }
}

// 获取检索列表 (plants_list)
const getPlantsList = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query
    const offset = (parseInt(page) - 1) * parseInt(limit)
    
    console.log(`获取检索列表请求: page=${page}, limit=${limit}, offset=${offset}`)
    console.log('开始查询plants_list集合...')
    
    // 查询数据
    const [dataResult, countResult] = await Promise.all([
      db.collection('plants_list')
        .orderBy('hit', 'desc') // 按命中次数降序排列
        .skip(offset)
        .limit(parseInt(limit))
        .get(),
      db.collection('plants_list').count()
    ])
    
    console.log('plants_list查询结果:', {
      dataCount: dataResult.data?.length || 0,
      total: countResult.total || 0
    })
    
    const responseData = {
      data: dataResult.data || [],
      total: countResult.total || 0,
      page: parseInt(page),
      limit: parseInt(limit)
    }
    
    res.json(Response.success(responseData, '获取检索列表成功'))
  } catch (err) {
    console.error('获取检索列表失败:', err)
    console.error('错误详情:', err.message)
    res.status(500).json(Response.error(`获取检索列表失败: ${err.message}`))
  }
}

// 删除检索词
const deletePlantItem = async (req, res) => {
  try {
    const { id } = req.params

    console.log(`删除检索词请求: id=${id}`)

    if (!id) {
      return res.status(400).json(Response.error('ID不能为空', 400))
    }

    // 先检查文档是否存在
    const docExists = await db.collection('plants_list').doc(id).get()
    console.log('删除前文档存在检查:', {
      exists: !!docExists.data,
      docId: id
    })

    if (!docExists.data) {
      console.log('要删除的文档不存在，返回404')
      return res.status(404).json(Response.error('未找到要删除的记录', 404))
    }

    // 删除记录
    const result = await db.collection('plants_list')
      .doc(id)
      .remove()

    console.log('删除结果:', JSON.stringify(result, null, 2))

    // 腾讯云开发数据库删除成功时，通常不会抛出异常
    // 如果执行到这里，说明删除成功
    console.log('删除操作完成')
    res.json(Response.success(null, '删除成功'))

  } catch (err) {
    console.error('删除检索词失败:', err)
    console.error('错误详情:', err.message)
    console.error('错误堆栈:', err.stack)

    // 检查是否是文档不存在的错误
    if (err.message && (err.message.includes('not found') || err.message.includes('document does not exist'))) {
      res.status(404).json(Response.error('未找到要删除的记录', 404))
    } else {
      res.status(500).json(Response.error('删除失败: ' + err.message))
    }
  }
}

// 测试去重功能
const testDeduplication = async (req, res) => {
  try {
    const { source } = req.params

    // 验证数据源
    if (!['notice', 'searchWord'].includes(source)) {
      return res.status(400).json(Response.error('无效的数据源', 400))
    }

    console.log(`测试${source}集合的去重功能...`)

    // 调用去重函数
    const result = await getDeduplicatedSourceData(source, 1, 10)

    res.json(Response.success(result, `${source}集合去重测试成功`))
  } catch (err) {
    console.error('测试去重功能失败:', err)
    res.status(500).json(Response.error(`测试失败: ${err.message}`))
  }
}

module.exports = {
  getSourceData,
  updatePlantName,
  addToPlantsList,
  getPlantsList,
  deletePlantItem,
  testDeduplication
}