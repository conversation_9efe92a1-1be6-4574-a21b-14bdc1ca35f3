<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能去重功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #337ecc;
        }
        .stats {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .stat-item {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }
        .stat-info { background: #e1f3ff; color: #0066cc; }
        .stat-primary { background: #e6f7ff; color: #1890ff; }
        .stat-warning { background: #fff7e6; color: #fa8c16; }
        .stat-success { background: #f6ffed; color: #52c41a; }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border-color: #ffccc7;
            color: #ff4d4f;
        }
        .success {
            background: #f6ffed;
            border-color: #b7eb8f;
            color: #52c41a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能去重功能测试</h1>
        <p>测试SearchWords页面的智能去重功能是否正常工作</p>
        
        <div>
            <button class="button" onclick="testNoticeDeduplication()">测试Notice集合去重</button>
            <button class="button" onclick="testSearchWordDeduplication()">测试SearchWord集合去重</button>
            <button class="button" onclick="testPlantsListData()">查看Plants_list数据</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/search-words';
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'container';
            
            let statsHtml = '';
            if (content.deduplicationStats) {
                const stats = content.deduplicationStats;
                statsHtml = `
                    <div class="stats">
                        <div class="stat-item stat-info">智能去重统计</div>
                        <div class="stat-item stat-primary">原始: ${stats.originalTotal}条</div>
                        <div class="stat-item stat-warning">已存在: ${stats.duplicateCount}条</div>
                        <div class="stat-item stat-success">可添加: ${stats.availableTotal}条</div>
                    </div>
                `;
            }
            
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                ${statsHtml}
                <div class="result ${type}">${JSON.stringify(content, null, 2)}</div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testNoticeDeduplication() {
            try {
                const response = await fetch(`${API_BASE}/test-deduplication/notice`);
                const data = await response.json();
                
                if (data.code === 200) {
                    addResult('Notice集合去重测试结果', data.data, 'success');
                } else {
                    addResult('Notice集合去重测试失败', data, 'error');
                }
            } catch (error) {
                addResult('Notice集合去重测试错误', { error: error.message }, 'error');
            }
        }
        
        async function testSearchWordDeduplication() {
            try {
                const response = await fetch(`${API_BASE}/test-deduplication/searchWord`);
                const data = await response.json();
                
                if (data.code === 200) {
                    addResult('SearchWord集合去重测试结果', data.data, 'success');
                } else {
                    addResult('SearchWord集合去重测试失败', data, 'error');
                }
            } catch (error) {
                addResult('SearchWord集合去重测试错误', { error: error.message }, 'error');
            }
        }
        
        async function testPlantsListData() {
            try {
                const response = await fetch(`${API_BASE}/plants-list?limit=20`);
                const data = await response.json();
                
                if (data.code === 200) {
                    addResult('Plants_list数据查看', data.data, 'success');
                } else {
                    addResult('Plants_list数据查看失败', data, 'error');
                }
            } catch (error) {
                addResult('Plants_list数据查看错误', { error: error.message }, 'error');
            }
        }
    </script>
</body>
</html>
