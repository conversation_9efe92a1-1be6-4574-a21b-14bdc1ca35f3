# 检索词去重功能说明

## 🎯 功能概述

为SearchWords页面的"增加检索词管理"表格实现了智能去重功能，避免管理员添加重复的植物名称到检索词库中。

## ✨ 核心特性

### 1. 全量数据去重验证
- ✅ 对比 `notice` 和 `searchWord` 集合中的 `plantName` 字段
- ✅ 与 `plants_list` 集合中的 `name` 字段进行比较
- ✅ **确保所有数据都参与去重验证**（不受分页限制）
- ✅ 自动过滤已存在的重复植物名称

### 2. 智能过滤逻辑
- 🔍 获取 `plants_list` 中所有植物名称作为去重基准
- 🔍 遍历源集合（notice/searchWord）的所有数据
- 🔍 过滤掉已存在于 `plants_list` 中的植物名称
- 🔍 保留空的 `plantName` 字段（允许管理员编辑）

### 3. 实时统计信息
- 📊 显示原始数据总量
- 📊 显示过滤掉的重复数据量
- 📊 显示可添加的数据量
- 📊 动态更新统计信息

### 4. 自动刷新机制
- 🔄 添加植物到检索列表后自动刷新主表格
- 🔄 删除检索词后自动刷新主表格
- 🔄 确保去重状态实时更新

## 🏗️ 技术实现

### 后端实现

#### 新增API接口
```javascript
GET /api/search-words/source/:source/deduped
```

#### 去重算法流程
1. **获取基准数据**: 从 `plants_list` 集合获取所有 `name` 字段
2. **全量数据获取**: 获取源集合的所有数据（不分页）
3. **智能过滤**: 
   - 保留空的 `plantName`（可编辑）
   - 过滤已存在的植物名称
4. **分页处理**: 对去重后的数据进行分页
5. **统计信息**: 返回原始总量、去重后总量、重复数量

#### 关键代码片段
```javascript
// 获取已存在的植物名称
const plantsListResult = await db.collection('plants_list').get()
const existingPlantNames = new Set()
plantsListResult.data.forEach(item => {
  if (item.name && item.name.trim()) {
    existingPlantNames.add(item.name.trim())
  }
})

// 过滤重复数据
const filteredData = allSourceData.filter(item => {
  if (!item.plantName || !item.plantName.trim()) {
    return true // 保留空值
  }
  return !existingPlantNames.has(item.plantName.trim())
})
```

### 前端实现

#### UI增强
- 📱 在数据源选择区域添加去重统计信息
- 🏷️ 使用不同颜色的标签显示统计数据
- 📊 实时显示原始数据量、重复数量、可添加数量

#### 自动刷新逻辑
```javascript
// 添加到检索列表后刷新
const handleAddToPlantsList = async (row) => {
  // ... 添加逻辑
  if (response.code === 200) {
    loadPlantsList() // 刷新检索列表
    loadData()       // 刷新主表格（更新去重状态）
  }
}
```

## 🎨 用户界面

### 统计信息显示
```
[ℹ️ 原始数据: 1500 条] [⚠️ 已过滤重复: 300 条] [📋 可添加: 1200 条]
```

### 颜色编码
- **蓝色标签**: 原始数据统计
- **绿色标签**: 无重复数据
- **橙色标签**: 有重复数据被过滤
- **主色标签**: 可添加数据量

## 🧪 测试验证

### 测试脚本
运行以下命令测试去重功能：
```bash
cd backend
node scripts/testDeduplication.js
```

### 测试内容
- ✅ 验证 `plants_list` 数据获取
- ✅ 测试 `notice` 集合去重
- ✅ 测试 `searchWord` 集合去重
- ✅ 显示重复数据示例
- ✅ 统计去重效果

## 🔧 配置说明

### 环境要求
- Node.js 环境
- 腾讯云开发数据库访问权限
- Vue 3 + Element Plus 前端环境

### 数据库集合
- `plants_list`: 检索词库（基准数据）
- `notice`: 通知集合（源数据1）
- `searchWord`: 搜索词备份集合（源数据2）

## 📈 性能优化

### 后端优化
- 🚀 使用 Set 数据结构进行快速查找
- 🚀 一次性获取所有基准数据，避免重复查询
- 🚀 在内存中进行去重处理，提高效率

### 前端优化
- 🚀 智能缓存去重结果
- 🚀 按需刷新数据
- 🚀 优化UI渲染性能

## 🛡️ 注意事项

1. **数据一致性**: 确保去重比较时字符串trim处理一致
2. **性能考虑**: 大数据量时可能需要优化内存使用
3. **用户体验**: 提供清晰的统计信息和操作反馈
4. **错误处理**: 完善的异常处理和用户提示

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 实现全量数据去重验证
- ✅ 添加实时统计信息显示
- ✅ 实现自动刷新机制
- ✅ 完善用户界面和交互体验
