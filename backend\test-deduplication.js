// 测试智能去重功能
const { db } = require('./config/database')

async function testDeduplication() {
  try {
    console.log('开始测试智能去重功能...')
    
    // 1. 获取plants_list中所有的name字段作为去重基准
    console.log('1. 获取plants_list中所有name字段...')
    const plantsListResult = await db.collection('plants_list').get()
    const existingNames = new Set()
    
    if (plantsListResult.data) {
      plantsListResult.data.forEach(item => {
        if (item.name && typeof item.name === 'string') {
          existingNames.add(item.name.trim().toLowerCase())
        }
      })
    }
    
    console.log(`plants_list中已存在${existingNames.size}个检索词:`)
    console.log([...existingNames].slice(0, 10)) // 显示前10个
    
    // 2. 测试notice集合
    console.log('\n2. 测试notice集合去重...')
    const noticeResult = await db.collection('notice').limit(100).get()
    const noticeData = noticeResult.data || []
    
    console.log(`notice集合获取到${noticeData.length}条数据`)
    
    // 统计plantName字段
    let noticeWithPlantName = 0
    let noticeDuplicates = 0
    let noticeAvailable = 0
    
    noticeData.forEach(item => {
      if (item.plantName && typeof item.plantName === 'string') {
        noticeWithPlantName++
        const trimmedPlantName = item.plantName.trim()
        if (trimmedPlantName && existingNames.has(trimmedPlantName.toLowerCase())) {
          noticeDuplicates++
        } else if (trimmedPlantName) {
          noticeAvailable++
        }
      }
    })
    
    console.log(`notice统计结果:`)
    console.log(`- 有plantName的记录: ${noticeWithPlantName}`)
    console.log(`- 重复的记录: ${noticeDuplicates}`)
    console.log(`- 可添加的记录: ${noticeAvailable}`)
    
    // 3. 测试searchWord集合
    console.log('\n3. 测试searchWord集合去重...')
    const searchWordResult = await db.collection('searchWord').limit(100).get()
    const searchWordData = searchWordResult.data || []
    
    console.log(`searchWord集合获取到${searchWordData.length}条数据`)
    
    // 统计plantName字段
    let searchWordWithPlantName = 0
    let searchWordDuplicates = 0
    let searchWordAvailable = 0
    
    searchWordData.forEach(item => {
      if (item.plantName && typeof item.plantName === 'string') {
        searchWordWithPlantName++
        const trimmedPlantName = item.plantName.trim()
        if (trimmedPlantName && existingNames.has(trimmedPlantName.toLowerCase())) {
          searchWordDuplicates++
        } else if (trimmedPlantName) {
          searchWordAvailable++
        }
      }
    })
    
    console.log(`searchWord统计结果:`)
    console.log(`- 有plantName的记录: ${searchWordWithPlantName}`)
    console.log(`- 重复的记录: ${searchWordDuplicates}`)
    console.log(`- 可添加的记录: ${searchWordAvailable}`)
    
    console.log('\n测试完成！')
    
  } catch (error) {
    console.error('测试失败:', error)
  }
}

// 运行测试
testDeduplication()
