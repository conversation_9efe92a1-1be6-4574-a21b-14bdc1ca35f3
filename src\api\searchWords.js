import request from '../utils/request'

// 获取数据源数据 (notice 或 searchWord 集合)
export function getSourceData(source, params) {
  return request({
    url: `/api/search-words/source/${source}`,
    method: 'GET',
    params
  })
}

// 获取去重后的数据源数据 (notice 或 searchWord 集合，排除已存在于 plants_list 中的数据)
export function getSourceDataDeduped(source, params) {
  return request({
    url: `/api/search-words/source/${source}/deduped`,
    method: 'GET',
    params
  })
}

// 更新植物名称
export function updatePlantName(source, id, plantName) {
  return request({
    url: `/api/search-words/update/${source}/${id}`,
    method: 'PUT',
    data: {
      plantName
    }
  })
}

// 添加到检索列表 (plants_list)
export function addToPlantsList(name) {
  return request({
    url: '/api/search-words/plants-list',
    method: 'POST',
    data: {
      name
    }
  })
}

// 获取检索列表 (plants_list)
export function getPlantsList(params) {
  return request({
    url: '/api/search-words/plants-list',
    method: 'GET',
    params
  })
}

// 删除检索词
export function deletePlantItem(id) {
  return request({
    url: `/api/search-words/plants-list/${id}`,
    method: 'DELETE'
  })
}