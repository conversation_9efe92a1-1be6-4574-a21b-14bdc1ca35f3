import request from '../utils/request'

// 获取数据源数据 (notice 或 searchWord 集合)
export function getSourceData(source, params) {
  return request({
    url: `/api/search-words/source/${source}`,
    method: 'GET',
    params: {
      ...params,
      enableDeduplication: 'true' // 默认启用智能去重
    }
  })
}

// 获取数据源数据 (不去重版本，用于对比)
export function getSourceDataWithoutDeduplication(source, params) {
  return request({
    url: `/api/search-words/source/${source}`,
    method: 'GET',
    params: {
      ...params,
      enableDeduplication: 'false'
    }
  })
}

// 更新植物名称
export function updatePlantName(source, id, plantName) {
  return request({
    url: `/api/search-words/update/${source}/${id}`,
    method: 'PUT',
    data: {
      plantName
    }
  })
}

// 添加到检索列表 (plants_list)
export function addToPlantsList(name) {
  return request({
    url: '/api/search-words/plants-list',
    method: 'POST',
    data: {
      name
    }
  })
}

// 获取检索列表 (plants_list)
export function getPlantsList(params) {
  return request({
    url: '/api/search-words/plants-list',
    method: 'GET',
    params
  })
}

// 删除检索词
export function deletePlantItem(id) {
  return request({
    url: `/api/search-words/plants-list/${id}`,
    method: 'DELETE'
  })
}